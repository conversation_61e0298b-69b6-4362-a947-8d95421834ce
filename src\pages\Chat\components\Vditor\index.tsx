import { NEED_OPEN_AI_WRITE_APPS } from '@/config';
import Vditor from '@/libs/vditor-dist/index';
import { ChatModelState } from '@/models/chat';
import { debounce, dispatchInUtils, uupload } from '@/utils';
import { Toast } from '@douyinfe/semi-ui';
import classNames from 'classnames';
import htmlDocx from 'html-docx-js/dist/html-docx';
import React, { useEffect, useRef, useState } from 'react';
import { useSearchParams, useSelector } from 'umi';

import 'vditor/dist/index.css';
import './index.less';

// import '~vditor/src/assets/less/index';

interface VditorEditorProps {
  //value?: string; // 初始内容
  //onChange?: (value: string) => void; // 内容变化时的回调
  height?: number | string; // 编辑器高度，支持数字或字符串（如'79vh'）
  chatId?: string; //
  placeholder?: string; // 占位文本
  saveUrl?: string; // 保存接口地址
  saveMethod?: 'POST' | 'PUT'; // 保存方法
  //title?: string; // 文档标题
  //onClose?: () => void; // 新增关闭回调}
}
const VditorEditor: React.FC<VditorEditorProps> = ({
  //value = '',
  //onChange,
  chatId = '',
  height = 800,
  placeholder = '',
  saveUrl = '/api/content/save',
  saveMethod = 'POST',
  //title = '',
  //onClose,
}) => {
  const writeContent = useSelector((state: { aiWrite: { content: '' } }) => state.aiWrite.content);
  const [searchParams, setSearchParams] = useSearchParams();

  const fixArtContent = (content1: string) => {
    let obj: { title: string; content: string } = { title: '', content: '' };

    // 如果内容为空，直接返回空的标题和内容
    if (!content1 || content1.trim() === '') {
      obj.title = '';
      obj.content = '';
      return obj;
    }

    if (content1.trim().indexOf('##') === 0) {
      let _tmps = content1.trim().split('\n');
      obj.title = _tmps[0].replace('##', '').trim();

      _tmps.splice(0, 1);
      obj.content = _tmps.join('\n');
    } else if (content1.trim().indexOf('《') === 0) {
      let _tmps = content1.trim().split('\n');
      obj.title = _tmps[0].trim();

      _tmps.splice(0, 1);
      obj.content = _tmps.join('\n');
    } else {
      obj.title = '';
      obj.content = content1;
    }

    if (!obj.title) obj.title = content1.replace(/\*|#|@/g, '').slice(0, 22) + '...';
    obj.content += '\n ###\n';

    return obj;
  };

  const [editValue, setEditValue] = useState(writeContent || '');
  const [saving, setSaving] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const vditorRef = useRef<any>(null);

  const [writeXTitle, setWriteXTitle] = useState('');
  const [writeXContent, setWriteXContent] = useState('');

  const [writing, setWriting] = useState(false);

  const chatState = useSelector((state: { chat: ChatModelState }) => state.chat);
  const { realTimeContent, chatMsg } = chatState;

  const assistantMessages = chatMsg?.[chatId]
    ? (chatMsg?.[chatId]?.messages || []).filter((item) => item?.role === 'assistant')
    : [];
  const lastMessage = assistantMessages?.length ? assistantMessages.at(-1) : {};

  useEffect(() => {
    if (!chatMsg || !chatMsg[chatId]) {
      // 如果没有聊天消息，但有 writeContent，则正常显示内容
      if (writeContent) {
        let { title, content } = fixArtContent(writeContent);
        setWriteXTitle(title);
        setWriteXContent(content);
        setWriting(false);
      }
      return;
    }

    const messages = chatMsg[chatId].messages || [];
    if (!Array.isArray(messages)) {
      return;
    }

    // 判断是否正在撰写中
    const isWriting =
      lastMessage && (lastMessage.status === 'loading' || lastMessage.status === 'reasoning');

    if (isWriting) {
      setWriteXTitle('');
      setWriteXContent('正在为您写作，请稍等...');
      setWriting(true);
    } else {
      // 消息完成或其他状态，显示正常内容
      let { title, content } = fixArtContent(writeContent);
      setWriteXTitle(title);
      setWriteXContent(content);
      setWriting(false);
    }
  }, [chatMsg, writeContent, realTimeContent, chatId]);

  const dooScrollToEditorBottom = () => {
    if (NEED_OPEN_AI_WRITE_APPS.indexOf(pageMode) < 0) return;

    setTimeout(() => {
      let editDom = document.querySelector('#vditor .vditor-reset');
      if (editDom) {
        editDom.scrollTo({
          top: editDom.scrollHeight,
        });
      }
    }, 100);
  };

  const pageMode: string = useSelector(
    (state: { pageLayout: { mode: '' } }) => state.pageLayout.mode,
  );

  useEffect(() => {
    if (NEED_OPEN_AI_WRITE_APPS.includes(pageMode)) {
      dispatchInUtils({
        type: 'chat/setIsExpand',
        payload: true,
      });
    }
  }, [pageMode]);

  useEffect(() => {
    if (!realTimeContent) return;

    dispatchInUtils({
      type: 'aiWrite/setContent',
      payload: {
        content: realTimeContent,
      },
    });

    dooScrollToEditorBottom();
  }, [realTimeContent]);

  // 监听外部 value 变化
  useEffect(() => {
    if (vditorRef.current && writeXContent !== editValue) {
      try {
        vditorRef.current.setValue && vditorRef.current.setValue(writeXContent);
        setEditValue(writeXContent);
      } catch (e) {
        console.error('设置编辑器内容失败:', e);
      }
    } else if (writeXContent !== editValue) {
      // 即使编辑器还没初始化，也要更新 editValue
      setEditValue(writeXContent);
    }
  }, [writeXContent, editValue]);

  const saveDoc = async () => {
    if (vditorRef.current) {
      const content = vditorRef.current.getValue();
      setEditValue(content);
      setSaving(true);
      try {
        const response = await fetch(saveUrl, {
          method: saveMethod,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ content }),
        });

        if (!response.ok) {
          throw new Error('保存失败');
        }

        const result = await response.json();
        onChange?.(content);
        Toast.success('保存成功');
      } catch (error) {
        Toast.error('保存失败，请重试');
        console.error('保存失败:', error);
      } finally {
        setSaving(false);
      }
    }
  };

  const publishDoc = async () => {
    if (vditorRef.current) {
      const content = vditorRef.current.getValue();
      setSaving(true);
      try {
        const response = await fetch(saveUrl, {
          method: saveMethod,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            content,
            status: 'published',
          }),
        });

        if (!response.ok) {
          throw new Error('发布失败');
        }

        const result = await response.json();
        onChange?.(content);
        Toast.success('发布成功');
      } catch (error) {
        Toast.error('发布失败，请重试');
        console.error('发布失败:', error);
      } finally {
        setSaving(false);
      }
    }
  };

  const handleImageUpload = (files: File[], callback?: (path: string) => void) => {
    // 这里实现图片上传逻辑
    uupload(files[0]).then((dd) => {
      if (dd.code === 0 && dd.data) {
        if (callback) {
          callback(dd.data);
        }
        return dd.data;
      }
    });
  };

  const handleClose = () => {
    const newParams = new URLSearchParams(searchParams);
    newParams.delete('pagemode');
    setSearchParams(newParams);
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: '',
    });
    dispatchInUtils({
      type: 'chat/setIsExpand',
      payload: false,
    });
    dispatchInUtils({
      type: 'aiWrite/clearContent',
    });

    /*
    setIsClosing(true);
    // 等待动画完成后再返回上一页
    setTimeout(() => {
      history.back();
    }, 300); // 动画时长为300ms
    */
  };

  useEffect(() => {
    const createVditor = () => {
      const vditor = new Vditor('vditor', {
        // 获取当前容器高度
        height: window.innerHeight - 40,
        // height,
        mode: 'wysiwyg',
        placeholder,
        toolbar: [
          'emoji',
          'headings',
          'bold',
          'italic',
          'strike',
          'link',
          '|',
          'list',
          'ordered-list',
          'check',
          'outdent',
          'indent',
          '|',
          'quote',
          'line',
          'code',
          'inline-code',
          'insert-before',
          'insert-after',
          '|',
          'upload',
          'table',
          '|',
          'undo',
          'redo',
          // '|',
          // 'fullscreen',
          // 'edit-mode',
          // {
          //   name: 'more',
          //   toolbar: [
          //     'both',
          //     'code-theme',
          //     'content-theme',
          //     'export',
          //     'outline',
          //     'preview',
          //     'devtools',
          //     'info',
          //     'help',
          //   ],
          // },
          // '|',
          // {
          //   hotkey: '⌘-S',
          //   name: 'save',
          //   tipPosition: 's',
          //   tip: saving ? '保存中...' : '保存',
          //   className: 'right',
          //   icon: `<img style="height: 16px" src='https://img.58cdn.com.cn/escstatic/docs/imgUpload/idocs/save.svg'/>`,
          //   click: saveDoc,
          // },
          // {
          //   hotkey: '',
          //   name: 'publish',
          //   tipPosition: 's',
          //   tip: saving ? '发布中...' : '发布文章',
          //   className: 'right',
          //   icon: `<img style="height: 16px" src='https://img.58cdn.com.cn/escstatic/docs/imgUpload/idocs/publish.svg'/>`,
          //   click: publishDoc,
          // },
        ],
        after: () => {
          // 使用 writeXContent 而不是 editValue，确保初始化时显示正确内容
          const initialContent = writeXContent || editValue || '正在为您写作，请稍等...';
          vditor.setValue(initialContent);
        },
        // blur: saveDoc,
        upload: {
          accept: 'image/*',
          multiple: false,
          filename(name: string) {
            return name
              .replace(/[^(a-zA-Z0-9\u4e00-\u9fa5\.)]/g, '')
              .replace(/[\?\\/:|<>\*\[\]\(\)\$%\{\}@~]/g, '')
              .replace('/\\s/g', '');
          },
          handler(files: File[]) {
            const callback = (path: string) => {
              const name = files[0]?.name;
              let succFileText = '';
              if (vditor && vditor.vditor.currentMode === 'wysiwyg') {
                succFileText += `\n <img alt=${name} src="${path}">`;
              } else {
                succFileText += `  \n![${name}](${path})`;
              }
              document.execCommand('insertHTML', false, succFileText);
            };
            return handleImageUpload(files, callback);
          },
          url: 'https://sit-drcloud.bhidi.com/gateway/admin-api/infra/open/file/upload', // 这里应该是你的实际上传接口地址
        },
      });
      vditorRef.current = vditor;
      return vditor;
    };

    createVditor();

    // 清理函数
    return () => {
      if (vditorRef.current) {
        vditorRef.current.destroy();
      }
    };
  }, [saving, writeXContent]); // 添加 writeXContent 作为依赖，确保内容变化时重新初始化

  const handleCopyWord = async () => {
    if (!vditorRef.current) return;
    const html = vditorRef.current.getHTML();
    try {
      await navigator.clipboard.write([
        new ClipboardItem({
          // text/html 让 Word 按富文本解析
          'text/html': new Blob([html], { type: 'text/html' }),
          // 兼容粘贴纯文本
          'text/plain': new Blob([html], { type: 'text/plain' }),
        }),
      ]);
      Toast.success('复制富文本到剪贴板成功，可直接粘贴到 Word');
    } catch (err) {
      Toast.error('复制 HTML 失败，请检查剪贴板权限');
    }
  };
  const handleDownloadWord = debounce(() => {
    if (!vditorRef.current) return;
    const html = vditorRef.current.getHTML();
    const docxBlob = htmlDocx.asBlob(
      '<h1>' + writeXTitle.replace('《', '').replace('》', '') + '</h1>' + html,
    );
    var link = document.createElement('a');
    link.href = URL.createObjectURL(docxBlob);
    link.download = writeXTitle + '.docx';
    link.click();
  }, 300);

  return (
    <div
      className={classNames('editorWrap', { closing: isClosing })}
      style={{ height: window.innerHeight }}
    >
      <div className="editorHeader">
        <div className="editorTitle">{writeXTitle}</div>
        <div className="editorActions">
          <button className="copyBtn" onClick={handleCopyWord}>
            <svg
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
            >
              <path
                d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z"
                fill="currentColor"
              ></path>
              <path
                d="M704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"
                fill="currentColor"
              ></path>
            </svg>
          </button>
          <button className="downloadBtn" onClick={handleDownloadWord}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M8 3a.5.5 0 0 1 .5.5v7.793l2.146-2.147a.5.5 0 0 1 .708.708l-3 3-.007.007a.497.497 0 0 1-.685-.007l-3-3a.5.5 0 1 1 .708-.708L7.5 11.293V3.5A.5.5 0 0 1 8 3z"
              />
              <path
                fillRule="evenodd"
                d="M2 13.5A.5.5 0 0 1 2.5 13h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"
              />
            </svg>
          </button>

          <button className="closeBtn" onClick={handleClose}>
            <svg
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
            >
              <path
                d="M563.8 512l262.5-312.9c4.4-5.2 0.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9c-4.4 5.2-0.7 13.1 6.1 13.1h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"
                fill="currentColor"
              ></path>
            </svg>
          </button>
        </div>
      </div>
      <div
        id="vditor"
        className={classNames('vditor', {
          ['vditorScrollbar']: !lastMessage?.status || lastMessage?.status === 'complete',
        })}
      />
      {writing && (
        <div className="loadingContainer">
          <span className="text">AI 撰写中...</span>
        </div>
      )}
    </div>
  );
};

export default VditorEditor;
